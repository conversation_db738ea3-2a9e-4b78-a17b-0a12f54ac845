package com.ict.ycwl.pathcalculate.config;

import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.pathcalculate.common.exception.ApiKeyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;

/**
 * 全局异常处理器
 * 统一处理系统中的各种异常，提供友好的错误响应
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理数据库连接异常
     */
    @ExceptionHandler({SQLException.class, DataAccessException.class})
    public AjaxResult handleDatabaseException(Exception e, HttpServletRequest request) {
        log.error("数据库异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage(), e);
        
        // 检查是否是连接问题
        if (e.getMessage() != null && 
            (e.getMessage().contains("Connection refused") || 
             e.getMessage().contains("Communications link failure") ||
             e.getMessage().contains("Access denied"))) {
            return AjaxResult.error("数据库连接失败，请检查数据库配置和连接状态");
        }
        
        return AjaxResult.error("数据库操作失败，请稍后重试");
    }

    /**
     * 处理API密钥异常
     */
    @ExceptionHandler(ApiKeyException.class)
    public AjaxResult handleApiKeyException(ApiKeyException e, HttpServletRequest request) {
        log.warn("API密钥异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage());
        return AjaxResult.error(401, "API密钥无效或已过期");
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public AjaxResult handleBindException(BindException e, HttpServletRequest request) {
        log.warn("参数绑定异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage());
        String errorMessage = e.getBindingResult().getAllErrors().get(0).getDefaultMessage();
        return AjaxResult.error(400, "参数错误: " + errorMessage);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public AjaxResult handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        log.error("空指针异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage(), e);
        return AjaxResult.error("系统内部错误，请联系管理员");
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public AjaxResult handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.error("运行时异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage(), e);
        
        // 检查是否是文件读取异常
        if (e.getMessage() != null && e.getMessage().contains("masterDatasource.txt")) {
            return AjaxResult.error("数据源配置文件不存在，请检查系统配置");
        }
        
        return AjaxResult.error("系统运行异常: " + e.getMessage());
    }

    /**
     * 处理其他所有异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request) {
        log.error("未知异常 - URL: {}, 异常类型: {}, 错误: {}", 
                 request.getRequestURL(), e.getClass().getSimpleName(), e.getMessage(), e);
        return AjaxResult.error("系统异常，请稍后重试");
    }
}
