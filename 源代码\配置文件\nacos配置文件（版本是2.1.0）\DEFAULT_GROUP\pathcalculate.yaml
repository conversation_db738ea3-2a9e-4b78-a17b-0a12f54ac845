server:
  port: 8084
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      druid:           # 统一 Druid 配置
        # 所有数据源的默认 Druid 配置
        default-config:
          initial-size: 5
          max-active: 20
          min-idle: 5
          max-wait: 60000
          pool-prepared-statements: true
          max-pooled-prepared-statements: 20
          time-between-eviction-runs-millis: 60000
          min-evictable-idle-time-millis: 300000
          validation-query: SELECT 1
          test-while-idle: true
          test-on-borrow: false
          test-on-return: false
          filters: stat,wall,log4j2
       
      datasource:
        master:
          url: *******************************************************************************************************
          username: root
          password: 123
          driver-class-name: com.mysql.jdbc.Driver
        slave1:
          url: **************************************************************************************************************
          username: root
          password: 123
          driver-class-name: com.mysql.jdbc.Driver
        slave2:
          url: **************************************************************************************************************
          username: root
          password: 123
          driver-class-name: com.mysql.jdbc.Driver
        slave3:
          url: **************************************************************************************************************
          username: root
          password: 123
          driver-class-name: com.mysql.jdbc.Driver

        # master:
        #   url: *****************************************************************************************************
        #   username: root
        #   password: 123456
        #   driver-class-name: com.mysql.jdbc.Driver
        # slave1:
        #   url: ************************************************************************************************************
        #   username: root
        #   password: 123456
        #   driver-class-name: com.mysql.jdbc.Driver
        # slave2:
        #   url: ************************************************************************************************************
        #   username: root
        #   password: 123456
        #   driver-class-name: com.mysql.jdbc.Driver
        # slave3:
        #   url: ************************************************************************************************************
        #   username: root
        #   password: 123456
        #   driver-class-name: com.mysql.jdbc.Driver
  application:
    name: pathcalculate
  cloud:
    nacos:
      server-addr: localhost:8848 # nacos地址
  mvc:
    servlet:
      load-on-startup: 1
mybatis:
  type-aliases-package: com.ict.ycwl.pathcalculate.pojo
  configuration:
    map-underscore-to-camel-case: true
logging:
  level:
    cn.itcast: debug
knife4j:
  enable: false

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.ict.ycwl.pathcalculate.pojo
  global-config:
    db-config:
      id-type: auto
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
route:
  version-limit: 100   

ycwl:
  saveDbName: ycdb
  saveDbPath: E:\www\wwwroot\ycwl\db
jjking:
  dbPath: E:\\www\\wwwroot\\ycwl\\masterDatasource.txt
  MybatisFile: file:E:\\www\\wwwroot\\ycwl\\masterDatasource.txt
  URL: *************************************************************************************************
  USER: root
  PASSWORD: aA13717028793#


    # master:
        #   url: *******************************************************************************************************
        #   username: root
        #   password: 123
        #   driver-class-name: com.mysql.jdbc.Driver
        # slave1:
        #   url: **************************************************************************************************************
        #   username: root
        #   password: 123
        #   driver-class-name: com.mysql.jdbc.Driver
        # slave2:
        #   url: **************************************************************************************************************
        #   username: root
        #   password: 123
        #   driver-class-name: com.mysql.jdbc.Driver
        # slave3:
        #   url: **************************************************************************************************************
        #   username: root
        #   password: 123
        #   driver-class-name: com.mysql.jdbc.Driver