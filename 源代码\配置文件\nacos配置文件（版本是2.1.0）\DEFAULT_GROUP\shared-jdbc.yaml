jjking:
 dbPath: E:\\www\\wwwroot\\ycwl\\masterDatasource.txt
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      master:
        url: *************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave1:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave2:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave3:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      initial-size: 15
      min-idle: 15
      max-active: 200
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: ""
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: false
      connection-properties: false